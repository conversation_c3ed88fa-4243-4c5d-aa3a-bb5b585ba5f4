/**
 * Configuration for mapping form fields to their respective tabs
 * This configuration is used for dirty tracking to determine which tab has unsaved changes
 */

export const TAB_FIELD_MAPPING = {
  /**
   * Shared fields that appear in the header and affect the overall chart
   * These fields are not specific to any tab but changes here should enable the save button
   */
  shared: [
    'visitId', 
    'medicalRecordNumber', 
    'lastName', 
    'firstName', 
    'dob', 
    'age', 
    'edDos', 
    'edStart', 
    'edEnd', 
    'dischargeStatus', 
    'provider', 
    'isEd'
  ],
  
  /**
   * ED Tab specific fields
   * These fields are primarily managed within the ED tab content
   */
  ed: [
    'treatmentArea', 
    'edChartStatus', 
    'note', 
    'levelCheckboxStates' // E&M Level selections
  ],
  
  /**
   * Observation Tab specific fields
   * These fields are primarily managed within the Obs tab content
   */
  obs: [
    'obsStart', 
    'obsEnd', 
    'isObs'
  ],
  
  /**
   * Professional Fee Tab specific fields
   * These fields are primarily managed within the Profee tab content
   */
  profee: [
    'traumaActivation', 
    'specialNoCharge', 
    'criticalCareMins', 
    'mod25', 
    'mod59'
  ]
} as const;

/**
 * Type definitions for tab names
 */
export type TabName = keyof typeof TAB_FIELD_MAPPING;

/**
 * Type definition for all possible form field names
 */
export type FormFieldName = typeof TAB_FIELD_MAPPING[TabName][number];

/**
 * Helper function to get all fields for a specific tab
 */
export const getFieldsForTab = (tabName: TabName): readonly string[] => {
  return TAB_FIELD_MAPPING[tabName];
};

/**
 * Helper function to determine which tab a field belongs to
 */
export const getTabForField = (fieldName: string): TabName | null => {
  for (const [tabName, fields] of Object.entries(TAB_FIELD_MAPPING)) {
    if (fields.includes(fieldName as any)) {
      return tabName as TabName;
    }
  }
  return null;
};

/**
 * Get all field names across all tabs
 */
export const getAllFormFields = (): string[] => {
  return Object.values(TAB_FIELD_MAPPING).flat();
};
