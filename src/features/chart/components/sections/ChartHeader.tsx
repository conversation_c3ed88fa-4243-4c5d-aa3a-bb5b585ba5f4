import React from 'react';
import {
  <PERSON><PERSON>, Text<PERSON>ield, FormControlLabel, Checkbox, Toolbar, Button
} from '@mui/material';
import { useForm<PERSON>ontext, Controller } from 'react-hook-form';
import type { SxProps, Theme } from '@mui/material/styles';

interface ChartHeaderProps {
  textFieldSx: SxProps<Theme>;
}

const ChartHeader: React.FC<ChartHeaderProps> = ({ textFieldSx }) => {
  const { control } = useFormContext();
  return (
    <>
      {/* Row 1 - Grid Container */}
      <Grid container spacing={1} alignItems="center">
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="visitId"
            control={control}
            render={({ field }) => (
              <TextField label="Visit ID" variant="outlined" size="small" {...field} sx={textFieldSx} />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="medicalRecordNumber"
            control={control}
            render={({ field }) => (
              <TextField label="Medical Record #" variant="outlined" size="small" {...field} sx={textFieldSx} />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="lastName"
            control={control}
            render={({ field }) => (
              <TextField label="Last" variant="outlined" size="small" {...field} sx={textFieldSx} />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="firstName"
            control={control}
            render={({ field }) => (
              <TextField label="First" variant="outlined" size="small" {...field} sx={textFieldSx} />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="dob"
            control={control}
            render={({ field }) => (
              <TextField label="DOB" variant="outlined" size="small" {...field} sx={textFieldSx} />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="age"
            control={control}
            render={({ field }) => (
              <TextField label="Age" variant="outlined" size="small" {...field} sx={textFieldSx} InputProps={{ sx: { maxWidth: '80px' } }} />
            )}
          />
        </Grid>
      </Grid>

      {/* Row 2 - Grid Container */}
      <Grid container spacing={1} alignItems="center" sx={{ mt: 0.125 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="edDos"
            control={control}
            render={({ field }) => (
              <TextField label="ED DOS" variant="outlined" size="small" {...field} sx={textFieldSx} />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="edStart"
            control={control}
            render={({ field }) => (
              <TextField label="ED Start" variant="outlined" size="small" {...field} sx={textFieldSx} />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="edEnd"
            control={control}
            render={({ field }) => (
              <TextField label="ED End" variant="outlined" size="small" {...field} sx={textFieldSx} />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="obsStart"
            control={control}
            render={({ field }) => (
              <TextField label="OBS Start" variant="outlined" size="small" {...field} sx={textFieldSx} />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Controller
            name="obsEnd"
            control={control}
            render={({ field }) => (
              <TextField label="OBS End" variant="outlined" size="small" {...field} sx={textFieldSx} />
            )}
          />
        </Grid>
        <Grid
          item
          xs={12} sm={12} md={2}
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'column', md: 'column' },
            alignItems: { xs: 'flex-start', sm: 'flex-start', md: 'flex-start' },
            justifyContent: {
              xs: 'flex-start',
              sm: 'flex-start',
              md: 'flex-start'
            }
          }}
        >
          <Controller
            name="isEd"
            control={control}
            render={({ field: { onChange, value } }) => (
              <FormControlLabel
                control={<Checkbox checked={value} onChange={onChange} size="small" sx={{ backgroundColor: 'background.paper', borderRadius: 1, padding: '1px', '& .MuiSvgIcon-root': { fontSize: 20 } }} />}
                label="ED Required"
                sx={{ color: 'text.primary', mr: 0, marginLeft: '-2px' }}
              />
            )}
          />
          <Controller
            name="isObs"
            control={control}
            render={({ field: { onChange, value } }) => (
              <FormControlLabel
                control={<Checkbox checked={value} onChange={onChange} size="small" sx={{ backgroundColor: 'background.paper', borderRadius: 1, padding: '1px', '& .MuiSvgIcon-root': { fontSize: 20 } }} />}
                label="OBS Required"
                sx={{ color: 'text.primary', ml: 0, marginLeft: '-2px' }}
              />
            )}
          />
        </Grid>
      </Grid>
      <Toolbar sx={{ 
        display: 'flex', 
        gap: 0.75, 
        padding: 0, 
        margin: 0, 
        minHeight: 'auto', 
        width: '100%',
        flexWrap: { xs: 'wrap', lg: 'nowrap' }
      }}>
        <Button variant="contained" color="primary" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>Save</Button>
        <Button variant="outlined" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>Coding report</Button>
        <Button variant="outlined" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>Done/Next</Button>
        <Button variant="outlined" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>Datalink</Button>
        <Button variant="outlined" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>Chg Alloc</Button>
        <Button variant="outlined" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>Img Export</Button>
        <Button variant="outlined" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>View History</Button>
        <Button variant="outlined" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>Sort Med/Admin</Button>
        <Button variant="outlined" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>Clear</Button>
        <Button variant="outlined" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>Cancel</Button>
        <Button variant="outlined" size="small" sx={{ flex: { xs: 'none', lg: 1 }, whiteSpace: 'nowrap' }}>Delete</Button>
      </Toolbar>
    </>
  );
};

export default ChartHeader;
